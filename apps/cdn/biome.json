{"root": false, "$schema": "https://biomejs.dev/schemas/2.1.1/schema.json", "files": {"ignoreUnknown": true, "includes": ["**", "!**/.next/**", "!**/node_modules/**", "!**/public/inspect.js", "!**/public/inspect.src.js", "!worker-configuration.d.ts"]}, "linter": {"enabled": true, "rules": {"a11y": {"useKeyWithClickEvents": "warn", "useValidAnchor": "error"}, "style": {"noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}}}, "formatter": {"enabled": true, "lineWidth": 100, "indentStyle": "space", "indentWidth": 2}, "javascript": {"formatter": {"quoteStyle": "single", "jsxQuoteStyle": "single", "trailingCommas": "es5", "semicolons": "asNeeded"}}}