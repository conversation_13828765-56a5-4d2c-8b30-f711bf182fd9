---
title: "常见问题"
mode: "center"
icon: CircleHelp
---

# 常见问题

欢迎来到 Libra AI 平台常见问题页面！这个综合指南解答了关于使用 Libra 平台的最常见问题，从入门到高级功能。如果您在这里找不到所需的答案，请不要犹豫，通过 GitHub 社区联系我们，我们的团队和社区成员随时准备提供帮助。

本常见问题涵盖了从基本功能到高级特性的各个方面，为技术问题提供了解决方案，并提供了有关如何高效使用平台的实用技巧。通过本指南，您可以快速找到所需的信息，充分利用 Libra 平台的强大功能，确保更顺畅、更高效的开发体验。

## 入门指南

### 我可以用 Libra 构建什么？

Libra 使您能够创建广泛的应用程序，包括面向用户的平台、业务工具和用于市场验证的 MVP。发现无尽的可能性，并从我们蓬勃发展的社区中汲取灵感。

您可以创建各种应用程序，包括：

- **内容网站**：个人博客、作品展示、企业官网和技术文档
- **电商平台**：集成支付处理和库存管理的在线商城
- **效率工具**：任务管理系统、笔记应用和团队协作平台
- **社交应用**：社区论坛、交流平台和社交网络
- **企业应用**：客户关系管理系统、数据仪表板和业务分析平台
- **教育平台**：在线学习系统和课程管理网站

**社区展示：** 当您访问平台时，您会发现由我们社区创建的各种工具和应用。从功能原型到实验项目，这个集合展示了 Libra 的无限可能性。


### 如何开始我的项目？

使用 Libra 开始项目非常简单和灵活。选择最适合您偏好和资源的方法：

#### 基于提示的开发
Libra 的基于提示的系统使应用创建变得直观。只需在提示框中描述您想要构建的内容。描述越具体，结果越好。

**示例提示：** "为 SaaS 初创公司创建一个现代着陆页。设计应具有深紫色渐变背景，并带有发光的未来主义效果。"

从清晰和详细的提示开始，然后根据需要改进或调整您的项目。


#### 设计导入
**使用 Figma 设计 (即将到来）：** 如果您在 Figma 中有设计，可以截取其任何部分的屏幕截图。您可以直接将屏幕截图粘贴到 Libra 中，或拖放图像文件。按下 Enter 后，Libra 将把您的设计转换为交互式页面或应用。

**使用 Excalidraw 绘图：** 喜欢使用 Excalidraw 或类似工具来绘制 UI？截取绘图的屏幕截图，然后将其粘贴或拖放到 Libra 中。该平台将把您的草图转换为功能性应用。

#### 网站克隆
想要复制现有的网页？使用快捷键（如 Mac 上的 Cmd+Shift+4）或 GoFullPage Chrome 扩展等工具截取屏幕截图。将屏幕截图上传到 Libra，平台将重新创建网页的结构。

### 如何从社区复制应用 （即将到来）？

{/* 当您访问平台时，您会看到由社区创建的各种应用。要复制应用：

1. 浏览社区应用展示
2. 找到您感兴趣的项目
3. 点击项目打开详情页面
4. 点击右上角的"复制"按钮
5. 该应用将被复制到您的项目中，您可以开始自定义 */}

## 项目配置和管理

### 如何启用数据库来存储数据？

暂时不支持，敬请期待

### 如何在应用中启用 AI？

如果您的应用需要 AI 功能（如 AI 辅助、智能推荐等），内置的 AI 功能将自动启用。无需其他设置或配置。

### 如何更改域名？

点击侧边栏中的"站点"以访问域名设置页面。

**对于免费用户：** 无法设置自定义域名，只能使用默认的 libra.sh 子域名。

**对于专业版或高级版用户：** 当你完成部署之后，你可以在域名设置中添加自定义域名。

### 如何从已部署的页面中删除"Built with Libra"徽章？

对于免费用户，"Built with Libra"徽章无法删除。如果您升级到专业版或高级版计划，徽章将自动删除。

{/* ### 如何更改主题？

点击聊天中的 🎨 图标，选择主题，然后告诉 AI：

"更改主题"或"应用所选主题"。 */}

### 我应该在知识库中添加什么？

Libra 中的知识库是您项目的蓝图。使用它来定义功能、设计元素、目标，甚至特定的商业信息或行业专业知识。

建议添加的内容：
- **项目概述**：应用的核心目标和用途
- **关键功能**：必需的特性和功能需求
- **设计指南**：视觉风格、品牌要求和 UI 偏好
- **商业背景**：行业特定信息和业务逻辑
- **技术要求**：特定的技术栈或集成需求

将这些信息组织成类别有助于 AI 更好地理解您的需求。您还可以将这些类别直接链接到您的提示，确保清晰度、一致性和与项目需求的对齐，以获得更准确和量身定制的编码结果。

### 如何改善移动体验？

要使您的应用对移动设备友好，只需在聊天中告诉 AI：

"使用响应式布局以实现移动兼容性。"

平台将自动调整布局和样式，确保在各种设备上的最佳显示效果。

## 故障排除

### 如何修复"预览页面空白"问题？

如果预览页面显示为空白，请尝试以下步骤：

1. **刷新页面**：首先尝试刷新浏览器页面
2. **恢复历史版本**：通过点击预览窗口左上角的"历史记录"按钮恢复到以前的版本。这使您可以回滚到最后一个工作版本
3. **请求 AI 修复**：如果页面仍然空白，请在聊天中输入："我看到一个空白页面，请检查并修复。"
4. **复制项目**：创建项目的副本，然后删除有问题的原项目

### 如何修复浏览器控制台中的错误？

对于部分错误可以使用 autofix button 来自动修复

如果您在浏览器控制台中遇到错误，请复制错误消息并将其粘贴到聊天中。AI 可以自动诊断和修复问题。

**示例：**
```
请修复错误：react-dom.production.min.js:121 ReferenceError: useState is not defined at Dashboard ((index):194:88)
```

AI 将分析错误并提供相应的修复方案。

### 修复错误会消耗 AI 积分吗？

不，修复错误不会消耗任何 AI 积分。此功能使您可以专注于完善项目，而不必担心 AI 积分使用或额外费用。注意：为了防止滥用我们对修复错误的功能进行了一些限制。

## 代码导出和部署

### 我可以导出网站的代码文件吗？

是的，您可以在不部署项目的情况下导出网站的代码文件。要这样做：

1. 导航到要下载的网站项目
2. 点击预览面板顶部的"代码"选项卡
3. 点击位于左下角的"下载文件"按钮以下载代码文件

**注意：** 导出的文件不包括数据库中的数据或与项目相关的任何 AI 功能。导出的是静态代码文件，您可以在本地环境或其他平台上部署。

## 社区和支持

### 如何获得帮助？

我们提供多个支持渠道：

| 问题类型 | 推荐渠道 | 响应时间 |
|------------|-------------------|---------------|
| 🐛 **错误报告** | [GitHub Issues](https://github.com/nextify-limited/libra/issues) | 1-2 个工作日 |
| 💡 **功能请求** | [GitHub Discussions](https://github.com/nextify-limited/libra/discussions) | 3-5 个工作日 |
| ❓ **使用问题** | [GitHub 讨论区](https://github.com/nextify-limited/libra/discussions) | 1-2 个工作日 |
| 🏢 **商务咨询** | [<EMAIL>](mailto:<EMAIL>) | 1 个工作日 |

### 有社区论坛吗？

有的！加入我们活跃的 GitHub 社区，您可以：

- 从社区和团队获得帮助
- 分享您的项目并获得反馈
- 参与技术讨论和交流
- 访问功能预览和更新
- 与其他开发者建立联系

访问我们的 [GitHub 讨论区](https://github.com/nextify-limited/libra/discussions) 开始参与。

### 如何了解新功能的最新信息？

关注这些渠道获取最新更新：

- 🐦 **Twitter**：[@nextify2024](https://x.com/nextify2024) 获取开发更新
- 📖 **文档**：[docs.libra.dev](https://docs.libra.dev/) 获取功能指南
- 🌐 **网站**：[libra.dev](https://libra.dev) 获取主要版本发布

## 还有问题？

如果您在此常见问题中找不到问题的答案，我们随时为您提供帮助：

- 📧 **发邮件给我们**：[<EMAIL>](mailto:<EMAIL>) 进行商务咨询
- 🐛 **报告问题**：[GitHub Issues](https://github.com/nextify-limited/libra/issues) 报告错误和技术问题
- 💡 **请求功能**：[GitHub Discussions](https://github.com/nextify-limited/libra/discussions) 提出功能建议

我们的社区和团队随时准备帮助您在 Libra AI 平台上取得成功！
