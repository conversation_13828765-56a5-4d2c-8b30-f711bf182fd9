/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * use-subscription.ts
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

import { authClient } from '@libra/auth/auth-client'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import { useTRPC } from '@/trpc/client'
import { useMutation } from '@tanstack/react-query'

export function useSubscription() {
  const router = useRouter()
  const { data: session } = authClient.useSession()
  const { data: activeOrganization } = authClient.useActiveOrganization()
  const trpc = useTRPC()
  
  const isAuthenticated = !!session

  // Create Portal Session mutation
  const createPortalSessionMutation = useMutation(
    trpc.stripe.createPortalSession.mutationOptions({
      onSuccess: async (data) => {
        const url = data?.data?.url || ''
        if (url) {
          router.push(url)
        } else {
          toast.error('Failed to get portal URL')
        }
      },
      onError: (err) => {
        toast.error('Failed to access billing portal, Please try again.')
      },
    })
  )

  const handleManageSubscription = async () => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }
    
    try {
      await createPortalSessionMutation.mutateAsync({})
    } catch (error) {
    }
  }

  const handleUpgradeSubscription = async (planName: string, seats: number, isYearly: boolean) => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    try {
      if (planName.toLowerCase().includes('free')) {
        router.push('/dashboard')
        return
      }

      const referenceId = activeOrganization?.id
      if (!referenceId) {
        toast.error('Failed to get organization info')
        return
      }

      console.log('Upgrading subscription with params:', {
        plan: planName,
        annual: isYearly,
        referenceId: referenceId,
        seats: 1
      })

      const response = await authClient.subscription.upgrade({
        plan: planName,
        successUrl: "/dashboard",
        cancelUrl: "/#price",
        annual: isYearly,
        referenceId: referenceId,
        seats: 1,
        disableRedirect: false  // 确保不禁用重定向
      })

      console.log('Subscription upgrade response:', response)
      console.log('Response keys:', response ? Object.keys(response) : 'No response')

      if (response) {
        // 检查所有可能的 URL 属性
        const possibleUrlKeys = ['url', 'checkout_url', 'session_url', 'redirect_url']
        let checkoutUrl = null

        for (const key of possibleUrlKeys) {
          if (key in response && typeof response[key] === 'string' && response[key]) {
            checkoutUrl = response[key]
            console.log(`Found checkout URL in ${key}:`, checkoutUrl)
            break
          }
        }

        if (checkoutUrl) {
          console.log('Redirecting to Stripe Checkout URL:', checkoutUrl)
          window.location.href = checkoutUrl
        } else if ('id' in response) {
          console.log('Subscription created with ID:', response.id)
          console.log('Full response object:', JSON.stringify(response, null, 2))
          toast.success('Subscription created successfully')
          router.push('/dashboard')
        } else {
          console.log('Unexpected response format:', JSON.stringify(response, null, 2))
          toast.error('Unexpected response format. Please contact support.')
        }
      } else {
        console.error('No response received from subscription upgrade')
        toast.error('Failed to create subscription, please try again later')
      }
    } catch (error) {
      console.error('Subscription upgrade error:', error)

      // 尝试提取更详细的错误信息
      if (error && typeof error === 'object') {
        console.error('Error details:', JSON.stringify(error, null, 2))

        // 检查是否是网络错误
        if ('message' in error) {
          console.error('Error message:', error.message)
        }

        // 检查是否是 API 错误响应
        if ('response' in error) {
          console.error('API response error:', error.response)
        }
      }

      toast.error('Subscription upgrade failed, please try again later')
    }
  }

  return {
    isAuthenticated,
    handleUpgradeSubscription,
    handleManageSubscription,
    isCreatingPortalSession: createPortalSessionMutation.isPending
  }
}